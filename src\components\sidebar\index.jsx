import React from "react";
import { MessageIcon, ChatNew, DeleteChat, FoldIcon } from "@/components/icon";
import { Dropdown, Spin, Modal } from "antd";
import { FormattedMessage } from "umi";
import { isCN } from "@/utils/index";
import InfiniteScroll from "react-infinite-scroll-component";
import styles from "./index.module.less";

const Sidebar = ({
  isSiderOpen,
  onFold,
  conversations,
  activeConversationId,
  onSelectConversation,
  onNewConversation,
  onDeleteConversation,
  isFloating = false,
  isLoadingChats = false,
  isLoadingMoreChats = false,
  hasMoreChats = true,
  onLoadMoreChats,
}) => {
  // 对话操作菜单
  const getConversationMenuItems = (conversationId) => [
    {
      key: "delete",
      label: (
        <div
          style={{
            display: "flex",
            alignItems: "center",
            gap: "8px",
            color: "#F15A4D",
          }}
        >
          <DeleteChat style={{ color: "#F15A4D" }} />
          <span>
            <FormattedMessage id="chat-delete" />
          </span>
        </div>
      ),
    },
  ];

  const handleMenuClick = async (key, conversationId) => {
    if (key === "delete") {
      // 显示确认删除弹窗
      Modal.confirm({
        title: isCN ? "删除此会话？" : "Delete this chat?",
        content: isCN
          ? "会话被删除后将无法恢复"
          : "This chat will be permanently deleted and cannot be recovered.",
        okText: isCN ? "删除" : "Delete",
        cancelText: isCN ? "取消" : "Cancel",
        okType: "danger",
        onOk: async () => {
          try {
            const success = await onDeleteConversation(conversationId);
          } catch (error) {
            console.error(error);
          }
        },
      });
    }
  };

  return (
    <div
      className={`${styles.sidebarWrapper} ${!isSiderOpen ? styles.sidebarCollapsed : ""}`}
    >
      <div className={styles.contentLeftHeader}>
        <div
          className={styles.conAddBtn}
          onClick={onNewConversation}
          style={{ width: isSiderOpen && !isFloating ? "172px" : "216px" }}
        >
          <ChatNew />
          <FormattedMessage id="chat-new" />
        </div>
        {isSiderOpen && !isFloating && (
          <div className={styles.foldBtn} onClick={onFold}>
            <FoldIcon />
          </div>
        )}
      </div>
      <div className={styles.contentLeftContent}>
        {conversations.length > 0 && (
          <div className={styles.header}>
            <MessageIcon />
            <span>
              <FormattedMessage id="chat-recent" />
            </span>
          </div>
        )}

        <div className={styles.conversationList} id="conversationListContainer">
          {isLoadingChats ? (
            <div className={styles.loadingState}>
              <Spin size="small" />
            </div>
          ) : conversations.length > 0 ? (
            <InfiniteScroll
              dataLength={conversations.length}
              next={onLoadMoreChats}
              hasMore={hasMoreChats}
              scrollableTarget="conversationListContainer"
            >
              {conversations.map((conversation) => (
                <div
                  key={conversation.id}
                  onClick={() => onSelectConversation(conversation.id)}
                  className={`${styles.conversationItem} ${
                    activeConversationId === conversation.id
                      ? `${styles.active}`
                      : ""
                  }`}
                >
                  <div className={styles.conversationContent}>
                    <div className={styles.conversationTitle}>
                      {conversation.title}
                    </div>
                  </div>
                  <Dropdown
                    menu={{
                      items: getConversationMenuItems(conversation.id),
                      onClick: ({ key, domEvent }) => {
                        domEvent.stopPropagation();
                        handleMenuClick(key, conversation.id);
                      },
                    }}
                    trigger={["click"]}
                    placement="bottomRight"
                  >
                    <div
                      className={styles.action}
                      onClick={(e) => e.stopPropagation()}
                    >
                      <div />
                      <div />
                      <div />
                    </div>
                  </Dropdown>
                </div>
              ))}
            </InfiniteScroll>
          ) : null}
        </div>
      </div>
    </div>
  );
};

export default Sidebar;
