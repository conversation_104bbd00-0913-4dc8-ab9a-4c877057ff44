type RouteConfig = {
  path: string;
  component?: string;
  redirect?: string;
  wrappers?:string,
  layout?:string,
};

export const routesConfig: RouteConfig[] =  [
  { path: '/login', component: 'errorPages/403' },
  {
    path: '/',
    redirect: '/chat',
  },
  {
    path: '/chat', 
    component: '@/pages/chat',
    routes: [
      { path: '/chat/:id', component: '@/pages/chat' },
    ],
  },
  {path: '/403', component: 'errorPages/403'},
  {path: '/*', component: 'errorPages/404'},
]