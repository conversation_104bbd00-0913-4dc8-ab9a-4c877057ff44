{"private": true, "author": "shixucheng <<EMAIL>>", "scripts": {"dev": "umi run ./openBrowser.ts && cross-env UMI_APP_ENV=dev HOST=ai-dev.tendata.net umi dev", "dev-win": "cross-env UMI_APP_ENV=dev umi dev", "dev:test": "cross-env UMI_APP_ENV=uat umi dev", "dev:prod": "cross-env UMI_APP_ENV=prod umi dev", "build": "cross-env UMI_APP_ENV=prod umi build", "build:uat": "cross-env UMI_APP_ENV=uat umi build", "build:prod": "cross-env UMI_APP_ENV=prod umi build", "postinstall": "umi setup", "start": "npm run dev"}, "dependencies": {"@ant-design/icons": "^6.0.0", "antd": "^5.26.4", "js-cookie": "^3.0.5", "lodash": "^4.17.21", "react-infinite-scroll-component": "^6.1.0", "react-markdown": "^10.1.0", "rehype-highlight": "^7.0.2", "remark-gfm": "^4.0.1", "umi": "^4.4.11"}, "devDependencies": {"@types/express": "^5.0.3", "@types/js-cookie": "^3.0.6", "@types/react": "^18.0.33", "@types/react-dom": "^18.0.11", "@umijs/plugins": "^4.4.11", "cross-env": "^7.0.3", "typescript": "^5.0.3"}}