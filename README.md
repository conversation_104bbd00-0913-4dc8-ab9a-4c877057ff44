# tendata-ai-portal

<div><a name="readme-top"></a>

<div align="center"><img height="180" src="src/assets/8.jpg"></div>

<h1 align="center">Tendata AI</h1>

腾道AI

## ✨ 背景

- 🌈 前后端分离中间态方案。
- 📦 基于umijs4，基于browserHistory。
- 🌍 国际化语言支持。


## 📦 安装

```bash
pnpm i/ pnmp install
```

## ⌨️ 使用说明

```bash
$ git clone http://git.tendata.com.cn/tendata/develop/tendata-ai-portal.git
$ cd + 子目录
$ pnpm install
$ npm start
```
## 规范
项目使用了 Eslint、StyleLint 代码检测插件，Prettier 代码格式化插件等 .eslintrc.js 文件配置规则等

## 目录
![alt text](image.png)


## 其他说明
1、不要提交 .umi 临时文件到 git 仓库，因为它只是dev 时的临时文件目录，比如入口文件、路由等，都会被临时生成到这里。

2、app.ts是运行时配置 文件，可以在这里扩展运行时的能力（比如修改路由、修改 render 方法等），不了解项目级别请勿改动。

## 部署指南

## 更多
底座是 umi，umi 是一个 webpack 之上的整合工具。 umi 相比于 webpack 增加了运行时的能力，同时帮助我们配置了很多 webpack 的预设。
想了解他与ice的区别，请看这里：https://wiki.tendata.com.cn/pages/viewpage.action?pageId=97747213






