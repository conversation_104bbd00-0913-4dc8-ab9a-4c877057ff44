@import './main.less'; 

.container {
  background: #F7F7FC;
  height: 100%;
  position: relative;
  .header {
    height: 56px;
    display: flex;
    justify-content: space-between;
    padding-left: 24px;
    padding-right: 24px;
    align-items: center;
    .headerLeft {
      display: flex;
      justify-content: center;
      align-items: center;
      cursor: pointer;
      img {
        width: 128px;
        height: 28px;
      }
      .title {
        color: var(---B9, #002680);
        font-family: Roboto;
        font-size: 20px;
        font-style: normal;
        font-weight: 500;
        line-height: 24px; /* 120% */
        margin-left: 8px;
      }
    }
    .headerRight {
      display: flex;
      align-items: center;
      .chEnChangeBox {
        display: flex;
        width: 32px;
        height: 32px;
        justify-content: center;
        align-items: center;
        border-radius: 6px;
        cursor: pointer;
        margin-right: 16px;
        img {
          width: 32px;
          height: 32px;
        }
        &:hover {
          background: var(---, rgba(0, 0, 0, 0.04));
        }
      }
      .avatareBox {
        display: flex;
        width: 28px;
        height: 28px; 
        justify-content: center;
        align-items: center;
        border-radius: 6px;
        cursor: pointer;
        img {
          width: 28px;
          height: 28px; 
        }
      }
    }
  }
  .content {
    width: 100%;
    display: flex;
    min-height: calc(100vh - 56px);
    .contentLeft {
      height: 100%;
      width: 216px;
      padding-top: 12px;
      padding-left: 16px;
      padding-right: 16px;
      transition: width 0.3s cubic-bezier(.4,0,.2,1);
      // transition-timing-function:  cubic-bezier(.4,0,.2,1);
      .contentLeftHeader {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding-bottom: 16px;
        border-bottom: 1px solid #E8EBED;
        width: 216px;
        .conAddBtn {
          width: 150px;
          border-radius: 8px;
          border: 1px solid rgba(61, 104, 245, 0.40);
          background: rgba(61, 104, 245, 0.04);
          display: flex;
          padding: 8px 10px;
          justify-content: center;
          align-items: center;
          color: var(---B6, #3D68F5);
          /* body/regular */
          font-size: 16px;
          font-style: normal;
          font-weight: 400;
          line-height: 24px; /* 150% */
          cursor: pointer;
          svg {
            margin-right: 8px;
          }
        }
        .foldBtn {
          width: 32px;
          height: 32px;
          border-radius: 6px;
          cursor: pointer;
          display: flex;
          justify-content: center;
          align-items: center;
          &:hover {
            background: var(---, rgba(0, 0, 0, 0.04));
          }
          img {
            width: 18px;
            height: 18px;
          }
        }
      }
      .contentLeftContent {
        margin-top: 16px;
      }
    }
    .contentLeftCollapsed {
      width: 0;
      padding: 0;
      margin: 0;
      visibility: hidden;
    }
    .contentRight {
      background: url('../assets/bg.png') no-repeat center;
      background-size: contain;
      background-color: var(--vt-c-white);
      border-radius: 12px;
      border: 1px solid var(---, #EAEAEA);
      flex: 1;
      margin-right: 12px;
      margin-bottom: 12px;
      display: flex;
    }
    .contentLeftCollapsedHotZone {
      width: 60px;
      cursor: pointer;
      .openBtn {
          cursor: pointer;
          margin-left: 16px;
          margin-top: 14px;
          width: 32px;
          height: 32px;
          border-radius: 6px;
          display: flex;
          justify-content: center;
          align-items: center;
          &:hover {
            background: var(---, rgba(0, 0, 0, 0.04));
          }
          img {
            width: 18px;
            height: 18px;
          }
      }
    }
    .levitateContentLeft {
      position: absolute;
      display: flex;
      width: 216px;
      height: 748px;
      top: 106px;
      left: 20px;
      padding: 12px 16px 0px 16px;
      flex-direction: column;
      align-items: flex-start;
      flex-shrink: 0;
      border-radius: 12px;
      border: 1px solid var(---, #EAEAEA);
      background: var(---, #F7F7FC);
      box-shadow: 0px 12px 32px 0px rgba(0, 0, 0, 0.08);
      transform: translateX(-120%) !important;
      transition: 0.5s cubic-bezier(.4,0,.2,1);
      z-index: 80;
      .contentLeftHeader {
        display: flex;
        align-items: center;
        padding-bottom: 16px;
        border-bottom: 1px solid #E8EBED;
        width: 100%;
        .conAddBtn {
          width: 100%;
          border-radius: 8px;
          border: 1px solid rgba(61, 104, 245, 0.40);
          background: rgba(61, 104, 245, 0.04);
          display: flex;
          padding: 8px 10px;
          justify-content: center;
          align-items: center;
          color: var(---B6, #3D68F5);
          font-size: 16px;
          font-style: normal;
          font-weight: 400;
          line-height: 24px; /* 150% */
          cursor: pointer;
          svg {
            margin-right: 8px;
          }
        }
      }
      .contentLeftContent {
        margin-top: 16px;
      }
    }
    .levitateContentLeftOpen {
      transform: translateX(0) !important;
      transition: 0.5s cubic-bezier(.4,0,.2,1);
    }
  }
}