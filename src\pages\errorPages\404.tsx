import { FormattedMessage, history } from "umi";
import styles from "./index.less";
import notFoundImg from "@/assets/404.png";
import { Button } from "antd";

const notFoundPage = () => {
  
  return (
      <div className={styles.container}>
        <div className={styles.content}>
          <img src={notFoundImg} alt="401" />
          <div className={styles.contentTextBox}>
            <div className={styles.contentText}><FormattedMessage id="404-contentText" /></div>
            <div className={styles.contenSubtText}><FormattedMessage id="404-contentSubtText" /></div>
            <Button type="primary" style={{marginTop: 16}} onClick={() => history.replace("/")}><FormattedMessage id="404-content-button" /></Button>
          </div>
        </div>
        {/* <div className={styles.bottomText}><FormattedMessage id="content-bottom-ai-tip" /></div> */}
      </div>
  );
};

export default notFoundPage;
