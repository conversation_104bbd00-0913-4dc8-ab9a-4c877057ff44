import { message } from "antd";

/**
 * 获取当前语言设置
 * @returns {string} 'zh_CN' | 'en_US'
 */
export const getCurrentLocale = () => {
  try {
    const locale = localStorage.getItem("umi_locale");
    return locale || "zh_CN"; // 默认中文
  } catch (error) {
    console.warn("Failed to get locale from localStorage:", error);
    return "zh_CN"; // 默认中文
  }
};

/**
 * 判断当前是否为中文环境
 * @returns {boolean}
 */
export const isCN = getCurrentLocale() === "zh_CN";

/**
 * 根据错误码获取错误消息
 * @param {string} code 错误码
 * @returns {string} 错误消息
 */
export const errorMessage = (code) => {
  if (code === "CHAT_IN_PROGRESS_MAX" || code === "CHAT_IN_PROGRESS") {
    return message.warning(
      isCN
        ? "正在回答中的提问已达上限，在其中一个完成后再提问吧"
        : "You have reached the maximum limit for ongoing questions. Please start a new question after one has ended."
    );
  }
  if (code === "CHAT_TOTAL_IN_PROGRESS_MAX") {
    return message.warning(
      isCN
        ? "当前使用 Tendata AI 的用户太多啦，请稍后再试"
        : "Too many users are currently using Tendata AI. Please try again later."
    );
  }
  if (code === "TOKEN_USEAGE_MAX") {
    return message.warning(isCN ? "提问已达上限" : "Question limit reached.");
  }
  if (code === "CHAT_COUNT_MAX") {
    return message.warning(
      isCN
        ? "会话数量已达上限，请删除部分现有会话后再创建新会话"
        : "Chat limit reached. Please delete an old chat to make room for a new one."
    );
  }
  if (code === "500") {
    return message.warning(
      isCN
        ? "系统繁忙，请稍后再试"
        : "The system is busy. Please try again later."
    );
  }
};
