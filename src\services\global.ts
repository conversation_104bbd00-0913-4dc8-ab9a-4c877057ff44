import { AIRequest } from './request';
import urlConfig from './hostConfig'
const appEnv = process.env.UMI_APP_ENV || 'dev';
import Cookies from 'js-cookie';

// 检查登录状态
export async function checkToken(params: any) {
  const urlHost = appEnv === 'dev' ? '' : urlConfig[appEnv].accountHost;
  return AIRequest(`${urlHost}/api/auth/token/check-token`, {
    method: 'GET',
  });
}

// 获取权限信息
export async function getAuth(params: any) {
  const urlHost = appEnv === 'dev' ? '' : urlConfig[appEnv].accountHost;
  return AIRequest(`${urlHost}/api/auth/user/info`, {
    method: 'GET',
  });
}

// 退出登录
export async function logout(params: any) {
  const urlHost = appEnv === 'dev' ? '' : urlConfig[appEnv].loginHost;
  return AIRequest(`${urlHost}/api/auth/token/logout`, {
    method: 'GET',
  });
}

// 获取token
export const getToken = () => {
  const urlHost = appEnv === 'dev' ? '' : urlConfig[appEnv].loginHost;
  return AIRequest(`${urlHost}/api/auth/oauth2/token`, {
    method: 'POST',
    headers: {
      "Content-Type": "application/x-www-form-urlencoded",
      authorization: "Basic dGVuZGF0YTp0ZW5kYXRh",
    },
    data: new URLSearchParams({
      grant_type: "refresh_token",
      scope: "server",
      refresh_token: Cookies.get("refresh_token"),
    }).toString(), // 转为 urlencoded 字符串
  });
};