import { Request, Response } from "express";

export default {
  // 新的发送消息接口 - 使用新的数据格式
  "POST /api/ai/v1/messages": async (req: Request, res: Response) => {
    const { chatId, content } = req.body;

    // 模拟错误情况（可以通过特定内容触发）
    if (content?.userPrompt?.includes("测试错误")) {
      return res.status(400).json({
        code: "CHAT_IN_PROGRESS_MAX",
        success: false,
        message: "已有三个提问正在回答，在其中一个完成后再提问吧",
        data: null,
      });
    }

    res.setHeader("Content-Type", "text/event-stream");
    res.setHeader("Cache-Control", "no-cache");
    res.setHeader("Connection", "keep-alive");

    // 随机决定是否有思考过程（模拟真实情况）
    const hasThinking = Math.random() > 0.3; // 70%的概率有思考过程

    // 模拟思考内容
    const thinkingContents = [
      "1. 首先，我需要理解用户的问题...\n2. 分析问题的关键点：\n   - 主要目标是什么？\n   - 需要考虑哪些因素？\n3. 根据已知信息，我可以这样回答...",
      "让我仔细分析一下这个问题：\n1. 用户的核心需求是什么？\n2. 有哪些可能的解决方案？\n3. 每种方案的优缺点如何？\n4. 最适合的建议是什么？",
      "思考中...\n- 这个问题涉及多个方面\n- 需要综合考虑各种因素\n- 让我整理一下思路\n- 准备给出详细的回答",
    ];

    const mainContents = [
      "这是一个模拟的流式响应。\n\n我会一个字一个字地输出，让你看到打字效果。\n\n这样的效果是不是很像真实的AI在思考和回复呢？\n\n我们可以设置不同的延迟来模拟不同的打字速度。",
      "这是一个没有思考过程的直接回复。\n\n有时候AI会直接给出答案，不需要显示思考过程。\n\n这样可以让回复更加简洁高效。",
      "根据您的问题，我来为您详细解答。\n\n这个问题确实很有意思，涉及到多个方面的考虑。\n\n让我从几个角度来分析一下...",
      "感谢您的提问！\n\n基于我的理解，这个问题可以这样来看：\n\n首先，我们需要明确几个关键点...",
      "这是一个很好的问题。\n\n让我为您提供一个全面的回答，希望能够帮助到您。",
    ];

    const thinkingContent = hasThinking
      ? thinkingContents[Math.floor(Math.random() * thinkingContents.length)]
      : "";
    const mainContent =
      mainContents[Math.floor(Math.random() * mainContents.length)];

    // 随机决定是否有tips（模拟真实情况）
    const hasTips = Math.random() > 0.5; // 50%的概率有tips
    const tipsOptions = [
      ["基于背景调查帮我写一封开发信"],
      ["写一封开发信", "进行背景调查"],
      ["制定营销策略", "分析竞争对手", "优化产品方案"],
      ["基于背景调查帮我写一封开发信", "生成客户分析报告"],
    ];
    const tips = hasTips
      ? tipsOptions[Math.floor(Math.random() * tipsOptions.length)]
      : [];

    try {
      await new Promise((resolve) => setTimeout(resolve, 500));

      // 生成消息ID并首先发送
      const messageId = `msg_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`;
      res.write(`data: {"content":null,"thinkingContent":null,"tips":null,"id":"${messageId}"}\n\n`);
      await new Promise((resolve) => setTimeout(resolve, 100));

      if (hasThinking) {
        // 发送思考内容 - 逐字输出，使用新格式
        const thinkingChars = thinkingContent.split("");
        for (const char of thinkingChars) {
          res.write(
            `data: {"content":null,"thinkingContent":"${char.replace(/"/g, '\\"').replace(/\n/g, "\\n")}","tips":null}\n\n`
          );

          // 调整延迟
          if ([".", "。", "!", "！", "?", "？"].includes(char)) {
            await new Promise((resolve) => setTimeout(resolve, 200));
          } else if ([",", "，", ";", "；"].includes(char)) {
            await new Promise((resolve) => setTimeout(resolve, 100));
          } else if (["\n"].includes(char)) {
            await new Promise((resolve) => setTimeout(resolve, 150));
          } else {
            await new Promise((resolve) => setTimeout(resolve, 30));
          }
        }

        // 思考完成，开始回复
        await new Promise((resolve) => setTimeout(resolve, 800));
        await new Promise((resolve) => setTimeout(resolve, 300));
      }

      // 发送主要内容 - 逐字输出，使用新格式
      const contentChars = mainContent.split("");
      for (const char of contentChars) {
        res.write(
          `data: {"content":"${char.replace(/"/g, '\\"').replace(/\n/g, "\\n")}","thinkingContent":null,"tips":null}\n\n`
        );

        // 调整延迟
        if ([".", "。", "!", "！", "?", "？"].includes(char)) {
          await new Promise((resolve) => setTimeout(resolve, 150));
        } else if ([",", "，", ";", "；"].includes(char)) {
          await new Promise((resolve) => setTimeout(resolve, 80));
        } else if (["\n"].includes(char)) {
          await new Promise((resolve) => setTimeout(resolve, 100));
        } else {
          await new Promise((resolve) => setTimeout(resolve, 20));
        }
      }

      // 发送tips（如果有的话）
      if (hasTips && tips.length > 0) {
        await new Promise((resolve) => setTimeout(resolve, 200));
        res.write(
          `data: {"content":null,"thinkingContent":null,"tips":${JSON.stringify(tips)}}\n\n`
        );
      }

      // 发送结束标记
      await new Promise((resolve) => setTimeout(resolve, 200));
      res.end();
    } catch (error) {
      console.error("Stream error:", error);
      res.status(500).json({ error: "Internal server error" });
    }
  },
  // 创建会话
  "POST /api/ai/v1/chats": (req: Request, res: Response) => {
    return res.json({
      id: "new_chat_id",
      title: req.body.title || "新对话",
    });
  },

  // 删除会话
  "DELETE /api/ai/v1/chats/:id": (req: Request, res: Response) => {
    return res.status(204).end(); // 或 res.json({ success: true })
  },

  // 修改会话
  "PUT /api/ai/v1/chats/:id": (req: Request, res: Response) => {
    return res.json({
      id: req.params.id,
      title: req.body.title,
    });
  },

  // 查询会话列表
  "GET /api/ai/v1/chats": (req: Request, res: Response) => {
    const offset = parseInt(req.query.offset as string) || 0;
    const limit = parseInt(req.query.limit as string) || 50;

    // 模拟会话数据 - 生成200条数据用于测试滚动加载
    const totalMockChats = 200;
    const titles = [
      "如何提高工作效率？",
      "Python编程入门指南",
      "机器学习算法详解",
      "前端开发最佳实践",
      "数据库设计原则",
      "React组件优化技巧",
      "TypeScript类型系统",
      "API接口设计规范",
      "微服务架构模式",
      "云计算技术趋势",
      "Vue.js响应式原理",
      "Node.js后端开发",
      "Docker容器化部署",
      "Kubernetes集群管理",
      "Redis缓存优化",
      "MySQL性能调优",
      "MongoDB文档数据库",
      "GraphQL API设计",
      "WebSocket实时通信",
      "JWT身份认证",
      "OAuth2授权机制",
      "HTTPS安全协议",
      "CDN内容分发",
      "负载均衡策略",
      "分布式系统设计",
      "消息队列应用",
      "ElasticSearch搜索",
      "数据可视化技术",
      "人工智能应用",
      "深度学习框架",
      "自然语言处理",
      "计算机视觉",
      "区块链技术",
      "物联网开发",
      "移动端开发",
      "跨平台框架",
      "性能监控工具",
      "代码质量管理",
      "持续集成部署",
      "敏捷开发方法",
      "项目管理技巧",
      "团队协作工具",
      "远程工作模式",
      "技术文档写作",
      "开源项目贡献",
      "编程最佳实践",
      "算法与数据结构",
      "系统架构设计",
      "软件测试策略",
      "用户体验设计",
    ];

    const mockChats = Array.from({ length: totalMockChats }, (_, index) => {
      const chatId = `chat_${index + 1}`;
      const title = titles[index % titles.length];
      const date = new Date();
      // 让日期更分散，有些是几小时前，有些是几天前，有些是几周前
      const hoursAgo = Math.floor(Math.random() * 24 * 30); // 30天内的随机时间
      date.setHours(date.getHours() - hoursAgo);

      return {
        id: chatId,
        title: `${title} (${index + 1})`,
        createdDate: date.toISOString(),
        lastMessage: index % 3 === 0 ? "这是最后一条消息的预览..." : undefined,
        timestamp: date.toISOString(),
      };
    });

    // 分页处理
    const total = mockChats.length;
    const content = mockChats.slice(offset, offset + limit);

    return res.json({
      offset,
      limit,
      total,
      content,
    });
  },

  // 查询消息列表
  // 消息反馈接口
  "POST /api/ai/v1/messages/:id/feedback": (req: Request, res: Response) => {
    const messageId = req.params.id;
    const { like } = req.body;

    // 模拟成功响应
    return res.json({
      success: true,
      message: "反馈已保存",
    });
  },

  "GET /api/ai/v1/messages": (req: Request, res: Response) => {
    const chatId = req.query.chatId as string;
    const offset = parseInt(req.query.offset as string) || 0;
    const limit = parseInt(req.query.limit as string) || 50;

    // 模拟消息数据 - 生成更多数据用于测试
    const totalMessages = 100; // 每个会话100条消息
    const userQuestions = [
      "请帮我分析一下这个问题",
      "如何优化这个方案？",
      "能详细解释一下原理吗？",
      "有什么更好的建议？",
      "这种做法的优缺点是什么？",
      "请提供一些实际案例",
      "如何避免常见的错误？",
      "最佳实践是什么？",
      "有哪些注意事项？",
      "能推荐一些学习资源吗？",
      "如何进行性能优化？",
      "安全方面需要考虑什么？",
      "如何进行测试？",
      "部署时要注意什么？",
      "如何进行维护？",
    ];

    const aiResponses = [
      "根据您的问题，我来详细分析一下...",
      "这是一个很好的问题，让我从几个方面来回答...",
      "基于我的理解，我建议采用以下方案...",
      "您提到的这个问题确实很重要，我的建议是...",
      "从技术角度来看，我们可以这样处理...",
      "这种情况下，最佳实践通常是...",
      "让我为您详细解释一下原理和实现方法...",
      "根据行业经验，我推荐以下几种方法...",
      "这个问题涉及多个方面，我逐一为您分析...",
      "基于您的需求，我提供以下解决方案...",
    ];

    const mockMessages = Array.from({ length: totalMessages }, (_, index) => {
      const isUser = index % 2 === 0;

      if (isUser) {
        // 用户消息 - 添加实体引用示例
        const hasEntities = index % 8 === 0; // 减少实体引用的频率
        const entityId = `${2000 + (index % 100)}`;
        const entityNames = [
          "alibaba",
          "tencent",
          "baidu",
          "microsoft",
          "google",
          "amazon",
          "apple",
          "meta",
        ];
        const entityName = entityNames[index % entityNames.length];

        return {
          id: `msg_${index + 1}`,
          role: "USER",
          contentType: "USER_CHAT",
          content: {
            userPrompt: hasEntities
              ? `生成@@${entityId}@@的背调报告，请详细分析@@${entityId}@@的业务情况`
              : `${userQuestions[index % userQuestions.length]} (消息 ${index + 1})`,
            entities: hasEntities
              ? [
                  {
                    id: entityId,
                    name: entityName,
                    country: index % 2 === 0 ? "CHN" : "USA",
                  },
                ]
              : [],
          },
        };
      } else {
        // AI助手消息
        // 有些消息有思考过程，有些没有（模拟真实情况）
        const hasThinkingContent = index % 3 !== 0; // 约2/3的消息有思考过程

        // 随机生成点赞状态：null(未评价), true(点赞), false(点踩)
        const likeStates = [null, null, null, true, false]; // 大部分未评价，少数有评价
        const randomLike = likeStates[index % likeStates.length];

        return {
          id: `msg_${index + 1}`,
          role: "ASSISTANT",
          contentType: "ASSISTANT_CHAT",
          like: randomLike,
          content: {
            content: `${aiResponses[index % aiResponses.length]}\n\n这是第 ${index + 1} 条AI回复，包含详细的解答内容和分析结果。我会根据您的具体需求提供针对性的建议和解决方案。`,
            thinkingContent: hasThinkingContent
              ? `思考过程 ${index + 1}：\n1. 首先分析问题的核心要点\n2. 考虑可能的解决方案\n3. 评估各种方案的优缺点\n4. 选择最适合的方案\n5. 制定具体的实施步骤`
              : "",
            tips:
              index % 4 === 0
                ? [
                    "写一封开发信",
                    "进行背景调查",
                    "制定营销策略",
                    "分析竞争对手",
                    "优化产品方案",
                  ]
                : [],
          },
        };
      }
    });

    // 分页处理
    const total = mockMessages.length;
    let content: any[] = [];

    if (offset === 0) {
      // 首次加载：获取最新的limit条消息
      // 例如：总共100条消息，limit=20，则取第80-99条（索引80-99）
      const startIndex = Math.max(0, total - limit);
      content = mockMessages.slice(startIndex);
      // 反转数组，让最新的消息在数组第一位，较早的消息在数组最后
      content.reverse();
    } else {
      // 加载更多历史消息：从已加载消息的前面继续加载
      // offset表示已经加载的消息数量
      const endIndex = Math.max(0, total - offset);
      const startIndex = Math.max(0, endIndex - limit);
      content = mockMessages.slice(startIndex, endIndex);
      // 反转数组，让相对较新的消息在数组第一位
      content.reverse();
    }

    return res.json({
      offset,
      limit,
      total,
      content,
    });
  },
  // 获取公司列表
  "GET /api/bizr/v1/internal/user/company": (req: Request, res: Response) => {
    const page = parseInt(req.query.page as string) || 0;
    const size = parseInt(req.query.size as string) || 20;

    // 模拟公司数据
    const mockCompanies = [
      {
        id: 1,
        tid: "ALI001",
        catalog: "Technology",
        name: "阿里巴巴",
        country: "CHN",
        source: "manual",
      },
      {
        id: 2,
        tid: "TEN001",
        catalog: "Technology",
        name: "腾讯",
        country: "CHN",
        source: "manual",
      },
      {
        id: 3,
        tid: "BAI001",
        catalog: "Technology",
        name: "百度",
        country: "CHN",
        source: "manual",
      },
      {
        id: 4,
        tid: "JD001",
        catalog: "E-commerce",
        name: "京东",
        country: "CHN",
        source: "manual",
      },
      {
        id: 5,
        tid: "HUA001",
        catalog: "Technology",
        name: "华为",
        country: "CHN",
        source: "manual",
      },
      {
        id: 6,
        tid: "XIA001",
        catalog: "Technology",
        name: "小米",
        country: "CHN",
        source: "manual",
      },
      {
        id: 7,
        tid: "BYT001",
        catalog: "Technology",
        name: "字节跳动",
        country: "CHN",
        source: "manual",
      },
      {
        id: 8,
        tid: "NET001",
        catalog: "E-commerce",
        name: "网易",
        country: "CHN",
        source: "manual",
      },
      {
        id: 9,
        tid: "MEI001",
        catalog: "E-commerce",
        name: "美团",
        country: "CHN",
        source: "manual",
      },
      {
        id: 10,
        tid: "DID001",
        catalog: "Transportation",
        name: "滴滴",
        country: "CHN",
        source: "manual",
      },
      // 添加更多数据用于测试分页
      ...Array.from({ length: 100 }, (_, index) => ({
        id: 11 + index,
        tid: `COM${String(11 + index).padStart(3, "0")}`,
        catalog: ["Technology", "E-commerce", "Finance", "Manufacturing"][
          index % 4
        ],
        name: `公司${11 + index}`,
        country: "CHN",
        source: "auto",
      })),
    ];

    const startIndex = page * size;
    const endIndex = startIndex + size;
    const content = mockCompanies.slice(startIndex, endIndex);

    return res.json({
      number: page,
      numberOfElements: content.length,
      totalElements: mockCompanies.length,
      totalPages: Math.ceil(mockCompanies.length / size),
      size,
      content,
    });
  },
};
