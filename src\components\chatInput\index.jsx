import React, { useState, useRef } from "react";
import { Space, Tooltip, Dropdown, Spin, Empty } from "antd";
import InfiniteScroll from "react-infinite-scroll-component";
import { ArrowUpFilled, SendFilled } from "@/components/icon";
import CountryFlag from "@/components/countryFlag";
import { getCompanyList } from "@/services/chat";
import { isCN } from "@/utils/index";
import { FormattedMessage } from "umi";
import styles from "./index.module.less";
import { useAIClickTrace } from "@/hooks/useTrace";

const ChatInput = ({
  onSendMessage,
  onPause,
  disabled = false,
  isGenerating = false,
}) => {
  const [selectedCompany, setSelectedCompany] = useState(null);
  const [isFocused, setIsFocused] = useState(false);
  const [companies, setCompanies] = useState([]);
  const [loading, setLoading] = useState(false);
  const [hasMore, setHasMore] = useState(true);
  const [page, setPage] = useState(0);
  const [dropdownOpen, setDropdownOpen] = useState(false);
  const dropdownRef = useRef(null);

  // 加载公司列表
  const loadCompanies = async (pageNum = 0, reset = false) => {
    if (loading) return;

    setLoading(true);
    try {
      const response = await getCompanyList({ page: pageNum, size: 20 });
      if (response && response.content !== undefined) {
        const newCompanies = response.content.map((company) => ({
          key: company.tid,
          label: company.name,
          ...company,
        }));

        if (reset) {
          setCompanies(newCompanies);
        } else {
          setCompanies((prev) => [...prev, ...newCompanies]);
        }

        const hasMoreData =
          response.totalPages > 0 && pageNum < response.totalPages - 1;
        setHasMore(hasMoreData);
        setPage(pageNum);
      } else {
        // 如果没有响应数据，设置为空状态
        if (reset) {
          setCompanies([]);
        }
        setHasMore(false);
      }
    } catch (error) {
      console.error(error);
    } finally {
      setLoading(false);
    }
  };

  // 处理下拉框开关
  const handleDropdownOpenChange = (open) => {
    setDropdownOpen(open);

    if (open) {
      // 每次打开时重置状态并重新加载数据
      setCompanies([]);
      setPage(0);
      setHasMore(true);
      loadCompanies(0, true);
    }
  };

  // 加载更多
  const loadMore = () => {
    if (!loading && hasMore) {
      loadCompanies(page + 1, false);
    }
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    if (isGenerating) {
      onPause?.();
      return;
    }
    if (selectedCompany && !disabled) {
      useAIClickTrace({ button_name: "AI_提问_主动发送问题" });
      // 构造新的消息格式
      const messageData = {
        userPrompt: isCN
          ? `为我生成一份@@${selectedCompany.tid}@@的背景报告`
          : `Generate a background report for @@${selectedCompany.tid}@@`,
        entities: [
          {
            id: selectedCompany.tid,
            name: selectedCompany.name || selectedCompany.label,
            country: selectedCompany.country,
          },
        ],
      };

      // 构造自定义参数，包含场景信息
      const customParams = {
        scene: "COMPANY_INFO",
      };

      onSendMessage(messageData, customParams);
      setSelectedCompany(null);
      setDropdownOpen(false);
    }
  };

  const handleCompanySelect = (company) => {
    setSelectedCompany(company);
    setDropdownOpen(false);
  };

  // 自定义下拉内容
  const popupRender = () => (
    <div className={styles.companyDropdown} id="companyScrollableDiv">
      {companies.length === 0 && !loading ? (
        // 空状态
        <div className={styles.emptyState}>
          <Empty
            image={Empty.PRESENTED_IMAGE_SIMPLE}
            description={<FormattedMessage id="radar-empty-message" />}
          />
        </div>
      ) : (
        <>
          <InfiniteScroll
            dataLength={companies.length}
            next={loadMore}
            hasMore={hasMore}
            loader={
              <div className={styles.loadingMore}>
                <Spin size="small" />
              </div>
            }
            scrollableTarget="companyScrollableDiv"
            height={280}
          >
            {companies.map((company) => (
              <div
                key={company.key}
                className={styles.companyItem}
                onClick={() => handleCompanySelect(company)}
              >
                <div className={styles.companyName}>
                  {CountryFlag({ code: company.country || "" })}
                  <span className={styles.companyNameText}>
                    {company.label}
                  </span>
                </div>
              </div>
            ))}
          </InfiniteScroll>
          <div className={styles.soure}>
            <div className={styles.text}>
              <FormattedMessage id="radar-data-source" />
            </div>
          </div>
        </>
      )}
    </div>
  );

  return (
    <div className={styles.inputContainer}>
      <form onSubmit={handleSubmit} className={styles.inputForm}>
        <div
          className={`${styles.inputArea} ${isFocused ? styles.focused : ""}`}
          tabIndex={0}
          onFocus={() => setIsFocused(true)}
          onBlur={(e) => {
            if (!e.currentTarget.contains(e.relatedTarget)) {
              setIsFocused(false);
            }
          }}
        >
          <div className={styles.fixedText}>
            <FormattedMessage id="input-generate-report-prefix" />
            <Dropdown
              open={dropdownOpen}
              onOpenChange={handleDropdownOpenChange}
              popupRender={popupRender}
              trigger={["click"]}
            >
              <div className={styles.companyText}>
                <span className={styles.lf}>[</span>
                <span
                  className={styles.company}
                  style={{ maxWidth: isCN ? "506px" : "480px" }}
                >
                  {selectedCompany ? (
                    selectedCompany.label
                  ) : (
                    <FormattedMessage id="input-select-company" />
                  )}
                </span>
                <span className={styles.rf}>]</span>
                <ArrowUpFilled className={styles.arrowIcon} />
              </div>
            </Dropdown>
            <FormattedMessage id="input-generate-report-suffix" />
          </div>
          {/* 发送按钮容器 */}
          <div
            style={{
              display: "flex",
              justifyContent: "flex-end",
              alignItems: "center",
            }}
          >
            <div
              className={styles.mainButton}
              data-disabled={!isGenerating && (!selectedCompany || disabled)}
              style={{
                background:
                  isGenerating || selectedCompany ? "#3D68F5" : "#BAD1FF",
                cursor:
                  !isGenerating && (!selectedCompany || disabled)
                    ? "not-allowed"
                    : "pointer",
              }}
              onClick={handleSubmit}
            >
              {isGenerating ? <div className={styles.pause} /> : <SendFilled />}
            </div>
          </div>
        </div>
      </form>
    </div>
  );
};

export default ChatInput;
