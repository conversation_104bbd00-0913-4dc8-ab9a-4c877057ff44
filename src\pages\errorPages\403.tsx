import { FormattedMessage, history } from "umi";
import { useEffect } from "react";
import styles from "./index.less";
import noAuthImg from "@/assets/403.png";
const _ = require('lodash');

const NoAuthPage = () => {
  useEffect(() => {
    if (localStorage.authInfo) {
      try {
        const authInfo =  JSON.parse(localStorage.authInfo);
        console.log(authInfo)
        const { permissions } = authInfo;
        const authAcess = _.includes(authInfo.permissions, 'AI:VIEW') 
        if (authAcess) history.push('/')
      } catch (error) {
        console.log('权限本地化加载失败 ===============>', error)
      }
    }
  }, []);
  
  return (
      <div className={styles.container}>
        <div className={styles.content}>
          <img src={noAuthImg} alt="403" />
          <div className={styles.contentTextBox}>
            <div className={styles.contentText}><FormattedMessage id="403-contentText" /></div>
            <div className={styles.contenSubtText}><FormattedMessage id="403-contentSubtText" /></div>
          </div>
        </div>
        {/* <div className={styles.bottomText}><FormattedMessage id="content-bottom-ai-tip" /></div> */}
      </div>
  );
};

export default NoAuthPage;
