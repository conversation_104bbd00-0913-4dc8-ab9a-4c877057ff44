const { exec } = require('child_process');
const os = require('os');

//打开浏览器的网页
const openWebUrl = () => {
    const { CLIENT_SSL, PORT } = process.env;
    const url = `${CLIENT_SSL}://localhost:${process.env.PORT}`;

    console.log()
    console.log(`即将打开 ==============>：${url}`)
    console.log()

    let type = os.platform()
    switch (type) {
        case 'win32':   //windows系统
            exec(`start ${url}`)
            break
        case 'darwin':  //苹果系统
            exec(`open ${url}`)
            break
        default:  //linux系统
            exec(`xdg-open ${url}`)
    }
}
openWebUrl()